#include "../stdafx.h"
#include "NSMySQLConnection.h"
#include "../MySQLCommand.h"
#include "../RecordSet.h"
#include "../MySQLCompatibility.h"
#include <sstream>
#include <chrono>

NSMySQLConnection::NSMySQLConnection()
{
    m_mysql = mysql_init(nullptr);
}

NSMySQLConnection::~NSMySQLConnection()
{
    Disconnect();
    if (m_mysql)
    {
        mysql_close(m_mysql);
        m_mysql = nullptr;
    }
}

bool NSMySQLConnection::Connect(const char* host, int port, const char* user,
                                const char* password, const char* database)
{
    if (!m_mysql)
        return false;

    // 연결 정보 저장
    m_host = host;
    m_port = port;
    m_user = user;
    m_password = password;
    m_database = database;

    // 연결 옵션 설정
    mysql_options(m_mysql, MYSQL_OPT_CONNECT_TIMEOUT, "5");
    mysql_options(m_mysql, MYSQL_OPT_READ_TIMEOUT, "30");
    mysql_options(m_mysql, MYSQL_OPT_WRITE_TIMEOUT, "30");
    
    // 자동 재연결 비활성화 (MySQL 8.0.34+에서 deprecated)
    // 수동 재연결 로직 사용 (Reconnect() 메서드)

    // 연결
    if (!mysql_real_connect(m_mysql, host, user, password, database, port, nullptr, 0))
    {
        m_connected = false;
        return false;
    }

    // UTF8 설정
    mysql_set_character_set(m_mysql, "utf8mb4");
    
    m_connected = true;
    return true;
}

void NSMySQLConnection::Disconnect()
{
    m_connected = false;
    
    // 캐시된 statement들 정리
    {
        std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
        for (auto& pair : m_stmtCache)
        {
            if (pair.second.stmt)
            {
                mysql_stmt_close(pair.second.stmt);
            }
        }
        m_stmtCache.clear();
        m_lruList.clear();
    }
    
    if (m_stmt)
    {
        mysql_stmt_close(m_stmt);
        m_stmt = nullptr;
    }
    
    if (m_result)
    {
        mysql_free_result(m_result);
        m_result = nullptr;
    }
    
    m_metadataCache.clear();
}

bool NSMySQLConnection::Reconnect()
{
    // 기존 연결 닫기
    if (m_mysql)
    {
        mysql_close(m_mysql);
        m_mysql = nullptr;
        m_connected = false;
    }
    
    // MYSQL 구조체 재초기화
    m_mysql = mysql_init(nullptr);
    if (!m_mysql)
        return false;

    // 재연결
    return Connect(m_host.c_str(), m_port, m_user.c_str(), 
                   m_password.c_str(), m_database.c_str());
}

bool NSMySQLConnection::IsConnected() const
{
    // mysql_ping() 대신 캐싱된 상태 사용 (스레드 안전)
    return m_mysql && m_connected;
}

bool NSMySQLConnection::CheckConnection()
{
    if (!m_mysql)
        return false;
        
    // 연결 상태가 닫혀있으면 재연결 시도
    if (!m_connected)
    {
        return Reconnect();
    }
    
    // 간단한 쿼리로 연결 확인 (SELECT 1)
    if (mysql_query(m_mysql, "SELECT 1") != 0)
    {
        m_connected = false;
        return Reconnect();
    }
    
    // 결과 소비
    MYSQL_RES* result = mysql_store_result(m_mysql);
    if (result)
    {
        mysql_free_result(result);
    }
    
    return true;
}

bool NSMySQLConnection::ExecuteProcedure(const std::string& procName, MySQLCommand* command)
{
    if (!m_mysql || !command)
        return false;
        
    // DB 호출 시점에 연결 확인 (ADO 방식)
    if (!CheckConnection())
        return false;

    // 프로시저 메타데이터 가져오기
    auto* metadata = GetProcedureMetadata(procName);
    if (!metadata)
    {
        if (!LoadProcedureMetadata(procName))
            return false;
        metadata = GetProcedureMetadata(procName);
    }

    // CALL 문 생성
    std::stringstream ss;
    ss << "CALL " << procName << "(";
    
    for (size_t i = 0; i < metadata->parameters.size(); ++i)
    {
        if (i > 0) ss << ", ";
        ss << "?";
    }
    ss << ")";

    std::string query = ss.str();

    // PreparedStatement 캐시 확인 (thread-safe LRU)
    {
        std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
        
        auto cacheIt = m_stmtCache.find(query);
        if (cacheIt != m_stmtCache.end())
        {
            // 캐시된 statement 사용
            m_stmt = cacheIt->second.stmt;
            cacheIt->second.lastUsed = std::chrono::steady_clock::now();
            
            // LRU 리스트에서 현재 항목을 앞으로 이동
            m_lruList.erase(cacheIt->second.lruIter);
            m_lruList.push_front(query);
            cacheIt->second.lruIter = m_lruList.begin();
        }
        else
        {
            // 새 statement 준비
            if (m_stmt)
                mysql_stmt_close(m_stmt);
            
            m_stmt = mysql_stmt_init(m_mysql);
            if (!m_stmt)
                return false;

            if (mysql_stmt_prepare(m_stmt, query.c_str(), query.length()) != 0)
            {
                mysql_stmt_close(m_stmt);
                m_stmt = nullptr;
                return false;
            }

            // 캐시가 가득 찬 경우 가장 오래된 항목 제거
            if (m_stmtCache.size() >= MAX_STMT_CACHE_SIZE)
            {
                // LRU 리스트의 마지막 항목이 가장 오래된 것
                std::string oldestQuery = m_lruList.back();
                m_lruList.pop_back();
                
                auto oldestIt = m_stmtCache.find(oldestQuery);
                if (oldestIt != m_stmtCache.end())
                {
                    mysql_stmt_close(oldestIt->second.stmt);
                    m_stmtCache.erase(oldestIt);
                }
            }

            // 새 항목 추가
            m_lruList.push_front(query);
            
            StmtCacheEntry entry;
            entry.stmt = m_stmt;
            entry.query = query;
            entry.lastUsed = std::chrono::steady_clock::now();
            entry.lruIter = m_lruList.begin();
            
            m_stmtCache[query] = entry;
        }
    }

    // 파라미터 바인딩
    std::vector<MYSQL_BIND> binds(metadata->parameters.size());
    command->BindToMySQL(m_stmt, binds);

    // 실행
    if (mysql_stmt_execute(m_stmt) != 0)
    {
        return false;
    }

    // OUT 파라미터 가져오기
    command->RetrieveOutputParameters(m_stmt);

    return true;
}

bool NSMySQLConnection::ExecuteQuery(const std::string& query)
{
    if (!m_mysql)
        return false;

    return mysql_query(m_mysql, query.c_str()) == 0;
}

std::unique_ptr<RecordSet> NSMySQLConnection::GetRecordSet()
{
    if (!m_mysql)
        return nullptr;

    if (m_result)
    {
        mysql_free_result(m_result);
        m_result = nullptr;
    }

    // Statement 결과인 경우
    if (m_stmt)
    {
        m_result = mysql_stmt_result_metadata(m_stmt);
        if (m_result)
        {
            // 결과를 클라이언트로 저장
            if (mysql_stmt_store_result(m_stmt) == 0)
            {
                return std::make_unique<RecordSet>(m_stmt, m_result);
            }
        }
    }
    // 일반 쿼리 결과인 경우
    else
    {
        m_result = mysql_store_result(m_mysql);
        if (m_result)
        {
            return std::make_unique<RecordSet>(m_mysql, m_result);
        }
    }

    return nullptr;
}

bool NSMySQLConnection::BeginTransaction()
{
    return ExecuteQuery("START TRANSACTION");
}

bool NSMySQLConnection::CommitTransaction()
{
    return ExecuteQuery("COMMIT");
}

bool NSMySQLConnection::RollbackTransaction()
{
    return ExecuteQuery("ROLLBACK");
}

bool NSMySQLConnection::ExecuteProcedureAsync(const std::string& procName, MySQLCommand* command)
{
    if (!m_mysql || !command)
        return false;

    // 프로시저 메타데이터 가져오기
    auto* metadata = GetProcedureMetadata(procName);
    if (!metadata)
    {
        if (!LoadProcedureMetadata(procName))
            return false;
        metadata = GetProcedureMetadata(procName);
    }

    // CALL 문 생성
    std::stringstream ss;
    ss << "CALL " << procName << "(";
    
    for (size_t i = 0; i < metadata->parameters.size(); ++i)
    {
        if (i > 0) ss << ", ";
        ss << "?";
    }
    ss << ")";

    std::string query = ss.str();

    // PreparedStatement 캐시에서 가져오기 (thread-safe LRU)
    {
        std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
        
        auto cacheIt = m_stmtCache.find(query);
        if (cacheIt != m_stmtCache.end())
        {
            m_stmt = cacheIt->second.stmt;
            cacheIt->second.lastUsed = std::chrono::steady_clock::now();
            
            // LRU 리스트에서 현재 항목을 앞으로 이동
            m_lruList.erase(cacheIt->second.lruIter);
            m_lruList.push_front(query);
            cacheIt->second.lruIter = m_lruList.begin();
        }
        else
        {
            // 새 statement 준비
            if (m_stmt)
                mysql_stmt_close(m_stmt);
            
            m_stmt = mysql_stmt_init(m_mysql);
            if (!m_stmt)
                return false;

            // 논블로킹 속성 설정
            mysql_stmt_attr_set(m_stmt, STMT_ATTR_ASYNC_ENABLE, (const void*)&m_asyncExecuting);

            if (mysql_stmt_prepare(m_stmt, query.c_str(), query.length()) != 0)
            {
                mysql_stmt_close(m_stmt);
                m_stmt = nullptr;
                return false;
            }

            // 캐시가 가득 찬 경우 가장 오래된 항목 제거
            if (m_stmtCache.size() >= MAX_STMT_CACHE_SIZE)
            {
                // LRU 리스트의 마지막 항목이 가장 오래된 것
                std::string oldestQuery = m_lruList.back();
                m_lruList.pop_back();
                
                auto oldestIt = m_stmtCache.find(oldestQuery);
                if (oldestIt != m_stmtCache.end())
                {
                    mysql_stmt_close(oldestIt->second.stmt);
                    m_stmtCache.erase(oldestIt);
                }
            }

            // 새 항목 추가
            m_lruList.push_front(query);
            
            StmtCacheEntry entry;
            entry.stmt = m_stmt;
            entry.query = query;
            entry.lastUsed = std::chrono::steady_clock::now();
            entry.lruIter = m_lruList.begin();
            
            m_stmtCache[query] = entry;
        }
    }

    // 파라미터 바인딩
    std::vector<MYSQL_BIND> binds(metadata->parameters.size());
    command->BindToMySQL(m_stmt, binds);

    // 비동기 실행 시작
    m_asyncExecuting = true;
    int status = 0;
    
    // mysql_stmt_execute_start 사용 (논블로킹)
    status = mysql_stmt_execute_start(&m_asyncStatus, m_stmt);
    
    if (status == 0)
    {
        // 즉시 완료됨
        m_asyncExecuting = false;
        command->RetrieveOutputParameters(m_stmt);
        return true;
    }
    
    // 비동기 처리 중
    return true;
}

bool NSMySQLConnection::PollAsyncResult()
{
    if (!m_asyncExecuting)
        return true;

    int status = 0;
    
    // mysql_stmt_execute_cont 사용하여 비동기 실행 계속
    status = mysql_stmt_execute_cont(&m_asyncStatus, m_stmt, 0);
    
    if (status == 0)
    {
        // 실행 완료
        m_asyncExecuting = false;
        return true;
    }
    
    // 아직 처리 중
    return false;
}

const char* NSMySQLConnection::GetLastError() const
{
    if (m_stmt)
        return mysql_stmt_error(m_stmt);
    if (m_mysql)
        return mysql_error(m_mysql);
    return "No error";
}

int NSMySQLConnection::GetLastErrorCode() const
{
    if (m_stmt)
        return mysql_stmt_errno(m_stmt);
    if (m_mysql)
        return mysql_errno(m_mysql);
    return 0;
}

bool NSMySQLConnection::LoadProcedureMetadata(const std::string& procName)
{
    // information_schema에서 프로시저 파라미터 정보 조회
    std::stringstream query;
    query << "SELECT PARAMETER_NAME, DATA_TYPE, PARAMETER_MODE "
          << "FROM information_schema.PARAMETERS "
          << "WHERE SPECIFIC_SCHEMA = '" << m_database << "' "
          << "AND SPECIFIC_NAME = '" << procName << "' "
          << "ORDER BY ORDINAL_POSITION";

    if (!ExecuteQuery(query.str()))
        return false;

    auto result = mysql_store_result(m_mysql);
    if (!result)
        return false;

    ProcedureMetadata metadata;
    MYSQL_ROW row;
    
    while ((row = mysql_fetch_row(result)))
    {
        ProcedureMetadata::Parameter param;
        param.name = row[0] ? row[0] : "";
        
        // 데이터 타입 매핑
        std::string dataType = row[1] ? row[1] : "";
        if (dataType == "int") param.type = MYSQL_TYPE_LONG;
        else if (dataType == "bigint") param.type = MYSQL_TYPE_LONGLONG;
        else if (dataType == "varchar") param.type = MYSQL_TYPE_VAR_STRING;
        else if (dataType == "datetime") param.type = MYSQL_TYPE_DATETIME;
        else param.type = MYSQL_TYPE_STRING;
        
        // 파라미터 모드
        std::string mode = row[2] ? row[2] : "";
        param.isOutput = (mode == "OUT" || mode == "INOUT");
        
        metadata.parameters.push_back(param);
    }
    
    mysql_free_result(result);

    // 캐시에 저장
    std::lock_guard<std::mutex> lock(m_metadataMutex);
    m_metadataCache[procName] = metadata;
    
    return true;
}

NSMySQLConnection::ProcedureMetadata* NSMySQLConnection::GetProcedureMetadata(const std::string& procName)
{
    std::lock_guard<std::mutex> lock(m_metadataMutex);
    auto it = m_metadataCache.find(procName);
    if (it != m_metadataCache.end())
        return &it->second;
    return nullptr;
}