# Database_Maria 라이브러리 종합 분석 리포트

**분석 일자**: 2025-07-22  
**분석 대상**: Database_Maria (MMO 게임서버용 MariaDB 라이브러리)  
**환경**: Windows, C++, 멀티프로세스, 최대 3000명/프로세스, 32개 DB 워커 스레드

---

## 📋 **전체 아키텍처 개요**

Database_Maria는 MMO 게임서버를 위한 MariaDB 전용 비동기 DB 라이브러리로, 다음과 같은 핵심 구조를 가지고 있습니다:

### 🏗️ **핵심 아키텍처**
- **멀티스레드 아키텍처**: 게임 로직 스레드 + DB 워커 스레드 (최대 32개)
- **CID 기반 샤딩**: 동일 CID는 동일 스레드/커넥션에서 처리하여 순서 보장
- **Promise 패턴**: 비동기 처리 후 원래 스레드로 결과 반환
- **연결 풀링**: 스레드별 전용 DB 커넥션 + 공유 풀 하이브리드 방식

### 🔧 **주요 구성요소**
- `NSDataBaseManager`: 싱글톤 DB 매니저 (연결 풀 관리, 스레드 라우팅)
- `NSMySQLConnectionPool`: 스레드별 연결 풀 관리
- `NSMySQLConnection`: MariaDB 연결 및 비동기 실행
- `ThreadedWorkManager`: DB 워커 스레드 관리
- `NSQueryData`: 쿼리 결과 및 RecordSet 관리

---

## ⚠️ **주요 문제점 및 위험 요소**

### 🔴 **1. 메모리 관리 문제 (심각)**

#### **DLL 경계 메모리 불일치**
```cpp
// mimalloc_integration.h
// 전역 new/delete 오버라이드 제거
// 필요한 경우 아래의 MimallocAllocator를 사용하여 명시적으로 mimalloc 사용

// NSQueryData.cpp - 문제 코드
NSQueryData::NSQueryData()
{
    m_pcDataSerializer = new NSDataSerializer();  // raw new 사용
}

NSQueryData::~NSQueryData()
{
    delete m_pcDataSerializer;  // raw delete 사용
}
```

**위험성**:
- DLL에서 mimalloc으로 할당하고 응용프로그램에서 CRT로 해제하는 경우 크래시 발생
- 일관성 없는 할당자 사용으로 메모리 오염 가능성

### 🔴 **2. 스레드 안전성 문제 (심각)**

#### **MariaDB 비동기 API 스레드 안전성**
```cpp
// NSMySQLConnection.cpp - 문제 코드
bool NSMySQLConnection::ExecuteProcedureAsync(const std::string& procName, MySQLCommand* command)
{
    m_asyncExecuting = true;  // atomic이 아님
    int status = mysql_stmt_execute_start(&m_asyncStatus, m_stmt);
    // 동일 연결에서 동시 호출 시 undefined behavior
}
```

**위험성**:
- `mysql_stmt_execute_start/cont`는 동일 연결에서 동시 호출 시 undefined behavior
- `m_asyncExecuting`, `m_asyncStatus` 등이 atomic이 아니어서 경합 조건 발생

### 🔴 **3. PreparedStatement 캐시 메모리 누수 (중간)**

```cpp
// NSMySQLConnection.h - 문제 코드
struct StmtCacheEntry
{
    MYSQL_STMT* stmt;
    std::string query;
    uint64_t lastUsed;
};
std::unordered_map<std::string, StmtCacheEntry> m_stmtCache;
static constexpr size_t MAX_STMT_CACHE_SIZE = 100;
```

**문제점**:
- LRU 구현 누락: 캐시 크기 제한은 있지만 실제 LRU 제거 로직이 없음
- MYSQL_STMT 해제 누락: 연결 종료 시 캐시된 statement들의 정리 로직 필요

### ⚠️ **4. 연결 풀 관리 문제 (중간)**

```cpp
// NSMySQLConnectionPool.cpp
std::unique_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection(int threadIndex)
{
    auto& tc = m_threadConnections[threadIndex];
    if (tc.connection && tc.connection->IsConnected())  // 단순 상태 검사
    {
        auto conn = std::move(tc.connection);
        tc.allocated = true;
        return conn;
    }
}
```

**문제점**:
- `IsConnected()`만으로는 실제 DB 연결 상태를 보장할 수 없음
- 재연결 로직이 복잡하고 예외 상황 처리 미흡

---

## ✅ **양호한 부분**

### **1. 순환참조 방지**
```cpp
// NSDataBaseManager.h - 좋은 예시
// Forward declarations
class NSDataSerializer;
class NSQueryData;
class NSStoredProcedure;
```
전방선언을 적절히 사용하여 헤더 의존성 최소화

### **2. CID 기반 순서 보장**
- 동일 CID는 동일 스레드/커넥션에서 처리
- 스레드 로컬 시퀀스로 순서 보장
- 데드락 위험 최소화

### **3. Promise 패턴 비동기 처리**
- 게임 로직 스레드 블로킹 방지
- 결과를 원래 스레드로 안전하게 반환

---

## 🛡️ **권장 개선사항**

### **1. 메모리 관리 통일 (최우선)**
```cpp
// DLL 전용 메모리 관리자 도입
class DLLSafeAllocator {
public:
    template<typename T, typename... Args>
    static std::unique_ptr<T> make_unique(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }
};

// NSQueryData 개선
class NSQueryData {
private:
    std::unique_ptr<NSDataSerializer> m_pcDataSerializer;
public:
    NSQueryData() : m_pcDataSerializer(DLLSafeAllocator::make_unique<NSDataSerializer>()) {}
};
```

### **2. 스레드 안전성 강화**
```cpp
class NSMySQLConnection {
private:
    std::atomic<bool> m_asyncExecuting{false};
    std::mutex m_asyncMutex;  // 비동기 작업 보호
    
public:
    bool ExecuteProcedureAsync(const std::string& procName, MySQLCommand* command) {
        std::lock_guard<std::mutex> lock(m_asyncMutex);
        if (m_asyncExecuting.load()) {
            return false;  // 이미 비동기 작업 진행 중
        }
        m_asyncExecuting = true;
        // ...
    }
};
```

### **3. 연결 상태 검증 강화**
```cpp
bool NSMySQLConnection::CheckConnection() {
    if (!m_connected) return false;
    
    // 실제 DB 쿼리로 연결 상태 확인
    if (mysql_query(m_mysql, "SELECT 1") != 0) {
        m_connected = false;
        return Reconnect();
    }
    
    // 결과 정리
    MYSQL_RES* result = mysql_store_result(m_mysql);
    if (result) mysql_free_result(result);
    
    return true;
}
```

### **4. PreparedStatement 캐시 개선**
```cpp
class LRUStmtCache {
private:
    struct CacheNode {
        MYSQL_STMT* stmt;
        std::string query;
        std::chrono::steady_clock::time_point lastUsed;
    };
    
    std::unordered_map<std::string, std::list<CacheNode>::iterator> m_cache;
    std::list<CacheNode> m_lruList;
    size_t m_maxSize;
    
public:
    void cleanup() {
        for (auto& node : m_lruList) {
            if (node.stmt) {
                mysql_stmt_close(node.stmt);
            }
        }
        m_cache.clear();
        m_lruList.clear();
    }
};
```

---

## 📊 **성능 및 안정성 평가**

| 항목 | 현재 상태 | 위험도 | 권장 조치 |
|------|-----------|--------|-----------|
| 메모리 관리 | ⚠️ 혼재 | **높음** | DLL 안전 할당자 도입 |
| 스레드 안전성 | ⚠️ 부분적 | **높음** | atomic 변수 및 mutex 추가 |
| 연결 풀링 | ✅ 양호 | 중간 | 상태 검증 로직 강화 |
| 순환참조 | ✅ 양호 | 낮음 | weak_ptr 사용 검토 |
| 캐시 관리 | ❌ 미흡 | 중간 | LRU 구현 및 정리 로직 추가 |

---

## 🎯 **즉시 조치 필요 사항**

### **우선순위 1 (즉시)**
1. **DLL 경계 메모리 관리 통일** - 크래시 방지
2. **비동기 API 스레드 안전성 보장** - 데이터 오염 방지

### **우선순위 2 (단기)**
3. **PreparedStatement 캐시 메모리 누수 방지** - 장기 운영 안정성
4. **연결 상태 검증 로직 강화** - 재연결 안정성

### **우선순위 3 (중기)**
5. **성능 모니터링 및 로깅 강화**
6. **단위 테스트 및 부하 테스트 추가**

---

## 📝 **결론**

Database_Maria 라이브러리는 전반적으로 잘 설계된 아키텍처를 가지고 있으나, **DLL 경계 메모리 관리**와 **스레드 안전성** 부분에서 심각한 문제점들이 발견되었습니다. 

특히 3000명 동시 접속 환경에서는 이러한 문제들이 크래시나 데이터 오염으로 이어질 수 있으므로, **즉시 개선이 필요**합니다.

권장 개선사항들을 단계적으로 적용하면 안정적이고 효율적인 DB 처리가 가능할 것입니다.

---

**분석자**: Augment Agent  
**검토 도구**: 코드베이스 분석, 웹 검색, 최신 문서 참조  
**참고 자료**: MariaDB Connector/C 문서, C++ 멀티스레딩 베스트 프랙티스, 게임서버 아키텍처 패턴
