# Database_Maria 상세 리팩토링 실행 계획

**작성일**: 2025-07-22  
**목표**: 플로우 파악 용이성 극대화 + 성능 유지  
**실행 기간**: 3주 (실 작업 15일)  
**작업 인원**: 개발자 1-2명

---

## 📋 목차

1. [리팩토링 범위와 목표](#1-리팩토링-범위와-목표)
2. [Week 1: 코드 가독성 개선](#2-week-1-코드-가독성-개선)
3. [Week 2: 디버깅 기능 추가](#3-week-2-디버깅-기능-추가)
4. [Week 3: 최적화 및 문서화](#4-week-3-최적화-및-문서화)
5. [구체적 구현 예시](#5-구체적-구현-예시)
6. [테스트 및 검증](#6-테스트-및-검증)

---

## 1. 리팩토링 범위와 목표

### 🎯 구체적 목표

| 항목 | 현재 상태 | 목표 상태 | 측정 방법 |
|------|-----------|-----------|-----------|
| 코드 이해 시간 | 신규 개발자 2주 | 3-4일 | 온보딩 추적 |
| 버그 위치 파악 | 평균 2시간 | 30분 이내 | 이슈 해결 시간 |
| 실행 흐름 추적 | 거의 불가능 | 로그로 즉시 파악 | 로그 분석 |
| 에러 원인 파악 | 정보 부족 | 상세 컨텍스트 제공 | 에러 리포트 |

### 📌 핵심 원칙

1. **기존 API 변경 없음** - 게임서버 코드 수정 불필요
2. **성능 영향 최소화** - 프로덕션은 ±2%, 디버그는 ±10% 허용
3. **점진적 개선** - 한 번에 하나씩, 테스트하며 진행
4. **실용적 접근** - 완벽보다는 실용성

---

## 2. Week 1: 코드 가독성 개선

### Day 1-2: 실행 흐름 주석 추가

#### 작업 대상 파일
- NSDataBaseManager.cpp
- AsyncDBWorker.cpp  
- NSMySQLConnectionPool.cpp
- DBPromise.h

#### 구체적 작업
```cpp
// NSDataBaseManager.cpp - StartQueryImpl 함수
template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(/*...*/)
{
    // ===== FLOW STEP 1: 게임 스레드에서 요청 시작 =====
    // - 호출자: 게임 로직 (예: 캐릭터 정보 조회)
    // - 현재 스레드: Game Logic Thread
    // - 다음 단계: CID 기반으로 DB Worker Thread 선택
    
    if (m_pushAccessProhibit.load()) {
        // FLOW: 서버 종료 중이면 요청 거부
        return DBPromise<>::CreateRejected(/*...*/);
    }
    
    // ===== FLOW STEP 2: CID 기반 스레드 라우팅 =====
    // - 동일 CID는 항상 같은 스레드로 (순서 보장)
    // - threadIndex = CID % 스레드수
    uint64_t shardKey = 0;
    if constexpr (requires { SP::Input; }) {
        // FLOW: 프로시저 입력에서 CID 추출
        NSDataSerializer tempSerializer = serializer;
        SP::Input input;
        tempSerializer >> input;
        if constexpr (requires { input.Cid; })
            shardKey = input.Cid;  // 캐릭터 ID
        else if constexpr (requires { input.Aid; })
            shardKey = input.Aid;  // 계정 ID
    }
    
    int threadIndex = GetExecutorByShardKey(shardKey);
    // FLOW: Thread[{threadIndex}]가 이 요청을 처리할 예정
    
    // ===== FLOW STEP 3: 비동기 작업 큐에 추가 =====
    // - 현재: Game Thread
    // - 다음: DB Worker Thread[threadIndex]에서 실행
    m_workerManager->PostWork([=]() mutable {
        // ===== FLOW STEP 4: DB Worker Thread에서 실행 시작 =====
        // - 현재 스레드: DB Worker Thread[threadIndex]
        // - 이 람다는 다른 스레드에서 실행됨!
        
        try {
            // FLOW: 연결 풀에서 전용 연결 획득
            auto conn = pool->GetConnection(threadIndex);
            
            // FLOW: 저장 프로시저 실행
            SP sp;  // 예: SpGetCharacterInfo
            auto result = sp.Execute(conn.get(), serializer);
            
            // FLOW: 실행 완료, Promise에 결과 설정
            promise.SetValue(queryData);
            
        } catch (...) {
            // FLOW: 에러 발생, Promise에 예외 전달
            promise.SetException(std::current_exception());
        }
        
    }, threadIndex);
    
    // ===== FLOW STEP 5: Promise 반환 =====
    // - 호출자는 Then()으로 결과 처리 콜백 등록
    // - 콜백은 원래 게임 스레드에서 실행됨
}
```

### Day 3-4: God Object 분리

#### NSDataBaseManager 리팩토링
```cpp
// NSDataBaseManager.h - Before
class NSDataBaseManager : public NSSingleton<NSDataBaseManager>
{
    // 400줄의 멤버 변수와 함수들...
};

// NSDataBaseManager.h - After
class NSDataBaseManager : public NSSingleton<NSDataBaseManager>
{
private:
    // ===== 내부 컴포넌트 클래스들 =====
    
    // 연결 풀 관리 전담
    class ConnectionPoolManager {
    private:
        std::array<std::unique_ptr<NSMySQLConnectionPool>, 12> m_pools;
        
    public:
        void Initialize(const DatabaseConfig& config);
        NSMySQLConnectionPool* GetPool(DatabaseType type, int shardId);
        void Shutdown();
        
        // 통계
        size_t GetTotalConnections() const;
        size_t GetActiveConnections() const;
    };
    
    // CID 기반 스레드 라우팅 전담
    class ThreadRouter {
    private:
        std::optional<uint32_t> m_threadCount;
        mutable std::array<std::atomic<int>, 10> m_shardCounters{0};
        
    public:
        void Initialize(uint32_t threadCount);
        int GetThreadIndex(int64_t shardKey) const;
        int GetNextSequence(int64_t cid) const;
    };
    
    // 쿼리 실행 추적 전담
    class QueryTracker {
    private:
        std::atomic<uint64_t> m_totalQueries{0};
        std::atomic<uint64_t> m_activeQueries{0};
        std::atomic<uint64_t> m_failedQueries{0};
        
    public:
        std::string StartQuery(const std::string& procName, int64_t cid);
        void EndQuery(const std::string& queryId, bool success, uint64_t elapsedMs);
        void LogSlowQuery(const std::string& queryId, const QueryInfo& info);
        
        QueryStatistics GetStatistics() const;
    };
    
    // 멤버 변수들
    ConnectionPoolManager m_connectionMgr;
    ThreadRouter m_threadRouter;
    QueryTracker m_queryTracker;
    std::unique_ptr<ThreadedWorkManager> m_workerManager;
    
public:
    // 기존 public 인터페이스는 그대로 유지
    // 내부 구현만 컴포넌트들에게 위임
};
```

### Day 5: 로그 개선

#### 구조화된 로그 포맷
```cpp
// 로그 매크로 개선
#define DB_LOG_QUERY_START(queryId, procName, cid, thread) \
    LOGD << "[QUERY_START]" \
         << " id=" << queryId \
         << " proc=" << procName \
         << " cid=" << cid \
         << " thread=" << thread \
         << " timestamp=" << GetTimestamp()

#define DB_LOG_QUERY_END(queryId, success, elapsed) \
    LOGD << "[QUERY_END]" \
         << " id=" << queryId \
         << " success=" << (success ? "true" : "false") \
         << " elapsed=" << elapsed << "ms"

#define DB_LOG_ERROR(queryId, error, context) \
    LOGE << "[QUERY_ERROR]" \
         << " id=" << queryId \
         << " error=" << error \
         << " " << context

// 사용 예시
std::string queryId = GenerateQueryId();
DB_LOG_QUERY_START(queryId, "SpGetCharacterInfo", cid, threadIndex);

// ... 실행 ...

DB_LOG_QUERY_END(queryId, true, elapsedTime);
```

---

## 3. Week 2: 디버깅 기능 추가

### Day 6-7: 쿼리 추적 시스템

#### QueryContext 구현
```cpp
// QueryContext.h - 새로 추가
class QueryContext {
public:
    struct Step {
        std::string name;
        std::chrono::steady_clock::time_point timestamp;
        std::thread::id threadId;
        
        uint64_t GetElapsedMs(const Step& prev) const {
            return std::chrono::duration_cast<std::chrono::milliseconds>
                   (timestamp - prev.timestamp).count();
        }
    };
    
private:
    std::string m_queryId;
    std::string m_procName;
    int64_t m_cid;
    std::chrono::steady_clock::time_point m_startTime;
    std::vector<Step> m_steps;
    std::map<std::string, std::string> m_context;
    
public:
    QueryContext(const std::string& procName, int64_t cid)
        : m_queryId(GenerateUUID())
        , m_procName(procName)
        , m_cid(cid)
        , m_startTime(std::chrono::steady_clock::now())
    {
        AddStep("Request received");
    }
    
    void AddStep(const std::string& stepName) {
        m_steps.emplace_back(Step{
            stepName, 
            std::chrono::steady_clock::now(),
            std::this_thread::get_id()
        });
        
        // 실시간 로그 (디버그 모드에서만)
        #ifdef _DEBUG
        LOGD << "[QUERY_STEP] " << m_queryId << " - " << stepName;
        #endif
    }
    
    void SetContext(const std::string& key, const std::string& value) {
        m_context[key] = value;
    }
    
    // 전체 실행 흐름을 한 번에 출력
    std::string GetFlowSummary() const {
        std::stringstream ss;
        ss << "\n========== Query Flow Summary ==========\n";
        ss << "Query ID: " << m_queryId << "\n";
        ss << "Procedure: " << m_procName << "\n";
        ss << "CID: " << m_cid << "\n";
        ss << "Total Time: " << GetTotalElapsedMs() << "ms\n";
        ss << "\nExecution Steps:\n";
        
        for (size_t i = 0; i < m_steps.size(); ++i) {
            const auto& step = m_steps[i];
            ss << std::setw(4) << i << ". " 
               << std::setw(30) << std::left << step.name
               << " [Thread: " << step.threadId << "]";
               
            if (i > 0) {
                ss << " (+" << step.GetElapsedMs(m_steps[i-1]) << "ms)";
            }
            ss << "\n";
        }
        
        if (!m_context.empty()) {
            ss << "\nContext:\n";
            for (const auto& [key, value] : m_context) {
                ss << "  " << key << ": " << value << "\n";
            }
        }
        
        ss << "========================================\n";
        return ss.str();
    }
};

// NSQueryData에 통합
class NSQueryData {
private:
    std::shared_ptr<QueryContext> m_queryContext;  // 추가
    
public:
    NSQueryData(const std::string& procName, int64_t cid) 
        : m_queryContext(std::make_shared<QueryContext>(procName, cid))
    {
        m_queryContext->AddStep("NSQueryData created");
    }
    
    ~NSQueryData() {
        // 소멸 시 전체 흐름 출력 (느린 쿼리만)
        if (m_queryContext->GetTotalElapsedMs() > 1000) {
            LOGW << m_queryContext->GetFlowSummary();
        }
    }
    
    QueryContext* GetContext() { return m_queryContext.get(); }
};
```

### Day 8-9: 에러 컨텍스트 강화

#### 상세 에러 정보
```cpp
// DatabaseError.h - 새로 추가
class DatabaseError {
public:
    enum class ErrorCode {
        None = 0,
        ConnectionFailed,
        QueryTimeout,
        DeadlockDetected,
        InvalidParameter,
        Unknown
    };
    
    struct ErrorContext {
        ErrorCode code;
        std::string message;
        std::string mysqlError;
        int mysqlErrno;
        std::string query;
        std::string queryId;
        int64_t cid;
        int threadIndex;
        std::string stackTrace;
        std::chrono::milliseconds executionTime;
        
        std::string ToString() const {
            std::stringstream ss;
            ss << "\n========== Database Error ==========\n";
            ss << "Error Code: " << static_cast<int>(code) << "\n";
            ss << "Message: " << message << "\n";
            ss << "MySQL Error: " << mysqlError << " (errno: " << mysqlErrno << ")\n";
            ss << "Query ID: " << queryId << "\n";
            ss << "Query: " << query << "\n";
            ss << "CID: " << cid << "\n";
            ss << "Thread: " << threadIndex << "\n";
            ss << "Execution Time: " << executionTime.count() << "ms\n";
            ss << "Stack Trace:\n" << stackTrace << "\n";
            ss << "===================================\n";
            return ss.str();
        }
    };
    
    static ErrorContext CaptureContext(
        ErrorCode code,
        const std::string& message,
        MYSQL* mysql,
        const QueryContext* queryContext)
    {
        ErrorContext ctx;
        ctx.code = code;
        ctx.message = message;
        
        if (mysql) {
            ctx.mysqlError = mysql_error(mysql);
            ctx.mysqlErrno = mysql_errno(mysql);
        }
        
        if (queryContext) {
            ctx.queryId = queryContext->GetQueryId();
            ctx.cid = queryContext->GetCid();
            ctx.executionTime = queryContext->GetElapsedTime();
        }
        
        ctx.threadIndex = GetCurrentThreadIndex();
        ctx.stackTrace = CaptureStackTrace();
        
        return ctx;
    }
};

// 사용 예시
if (mysql_stmt_execute(stmt) != 0) {
    auto error = DatabaseError::CaptureContext(
        DatabaseError::ErrorCode::QueryTimeout,
        "Failed to execute statement",
        mysql,
        queryContext
    );
    
    LOGE << error.ToString();
    throw DatabaseException(error);
}
```

### Day 10: 디버그 모드 기능

#### 개발자 친화적 디버그 모드
```cpp
// DebugMode.h
class DatabaseDebugMode {
private:
    static bool s_enabled;
    static bool s_logAllQueries;
    static bool s_captureExecutionPlans;
    static uint32_t s_slowQueryThresholdMs;
    
public:
    static void Enable() {
        s_enabled = true;
        LOGI << "Database Debug Mode ENABLED";
        
        // 모든 쿼리 로깅
        EnableQueryLogging();
        
        // 느린 쿼리 임계값 낮춤 (프로덕션: 1000ms, 디버그: 100ms)
        s_slowQueryThresholdMs = 100;
        
        // 실행 계획 캡처
        s_captureExecutionPlans = true;
    }
    
    static void LogQuery(const QueryContext& ctx) {
        if (!s_enabled || !s_logAllQueries) return;
        
        // 파일로 저장 (나중에 분석 도구에서 읽음)
        std::ofstream file("db_queries.log", std::ios::app);
        file << ctx.ToJson() << "\n";
    }
    
    static void CaptureExecutionPlan(MYSQL* mysql, const std::string& query) {
        if (!s_enabled || !s_captureExecutionPlans) return;
        
        std::string explainQuery = "EXPLAIN " + query;
        if (mysql_query(mysql, explainQuery.c_str()) == 0) {
            MYSQL_RES* result = mysql_store_result(mysql);
            // 실행 계획 저장...
            mysql_free_result(result);
        }
    }
};

// 디버그 모드 활성화 (개발 환경에서만)
#ifdef _DEBUG
    DatabaseDebugMode::Enable();
#endif
```

---

## 4. Week 3: 최적화 및 문서화

### Day 11-12: 코드 정리

#### 중복 코드 제거
```cpp
// DatabaseHelpers.h - 공통 유틸리티 함수
namespace DBHelpers {
    // CID로 스레드 인덱스 계산 (여러 곳에서 중복된 로직)
    inline int GetThreadIndexForCID(int64_t cid, uint32_t threadCount) {
        return static_cast<int>(cid % std::max(1u, threadCount));
    }
    
    // 쿼리 ID 생성 (UUID 대신 읽기 쉬운 포맷)
    inline std::string GenerateQueryId() {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>
                        (now.time_since_epoch()).count();
        
        // 포맷: Q_타임스탬프_카운터 (예: Q_1737526400000_12345)
        return "Q_" + std::to_string(timestamp) + "_" + 
               std::to_string(counter.fetch_add(1));
    }
    
    // 스택 트레이스 캡처 (Windows)
    inline std::string CaptureStackTrace() {
        #ifdef _WIN32
        void* stack[20];
        HANDLE process = GetCurrentProcess();
        SymInitialize(process, NULL, TRUE);
        WORD frames = CaptureStackBackTrace(0, 20, stack, NULL);
        
        std::stringstream ss;
        for (WORD i = 0; i < frames; i++) {
            DWORD64 address = (DWORD64)(stack[i]);
            DWORD64 displacement = 0;
            char buffer[sizeof(SYMBOL_INFO) + MAX_SYM_NAME * sizeof(TCHAR)];
            PSYMBOL_INFO symbol = (PSYMBOL_INFO)buffer;
            symbol->SizeOfStruct = sizeof(SYMBOL_INFO);
            symbol->MaxNameLen = MAX_SYM_NAME;
            
            if (SymFromAddr(process, address, &displacement, symbol)) {
                ss << i << ": " << symbol->Name << " + " 
                   << displacement << "\n";
            }
        }
        return ss.str();
        #else
        return "Stack trace not available";
        #endif
    }
}
```

#### 상수 정의 통합
```cpp
// DatabaseConstants.h
namespace DBConstants {
    // 스레드 관련
    constexpr uint32_t MAX_WORKER_THREADS = 64;
    constexpr uint32_t DEFAULT_WORKER_THREADS = 32;
    
    // 연결 풀 관련  
    constexpr uint32_t MAX_CONNECTIONS_PER_POOL = 100;
    constexpr uint32_t MIN_CONNECTIONS_PER_POOL = 10;
    constexpr uint32_t CONNECTION_TIMEOUT_SECONDS = 30;
    
    // 캐시 관련
    constexpr size_t STATEMENT_CACHE_SIZE = 100;
    constexpr size_t METADATA_CACHE_SIZE = 500;
    
    // 성능 임계값
    constexpr uint32_t SLOW_QUERY_THRESHOLD_MS = 1000;
    constexpr uint32_t DEADLOCK_RETRY_COUNT = 3;
    constexpr uint32_t DEADLOCK_RETRY_DELAY_MS = 100;
    
    // 데이터베이스 타입
    enum class DatabaseType {
        GameDB = 0,
        CommonDB = 10,
        LogDB = 11
    };
    
    // 샤드 개수
    constexpr int GAME_DB_SHARD_COUNT = 10;
}
```

### Day 13-14: 문서화

#### 플로우 다이어그램 생성
```cpp
// FlowDiagram.md - 자동 생성되는 문서
/*
# Database_Maria 실행 플로우

## 1. 쿼리 실행 흐름도

```mermaid
sequenceDiagram
    participant Game as Game Thread
    participant Manager as NSDataBaseManager
    participant Router as ThreadRouter
    participant Worker as DB Worker Thread
    participant Pool as Connection Pool
    participant DB as MariaDB
    
    Game->>Manager: StartQuery<SpGetUserInfo>()
    Manager->>Router: GetThreadIndex(CID)
    Router-->>Manager: threadIndex = 3
    Manager->>Worker: PostWork(lambda, threadIndex=3)
    
    Note over Worker: DB Worker Thread[3]에서 실행
    
    Worker->>Pool: GetConnection(threadIndex=3)
    Pool-->>Worker: connection
    Worker->>DB: CALL SpGetUserInfo(?)
    DB-->>Worker: ResultSet
    Worker->>Game: Promise.SetValue(result)
    
    Note over Game: 원래 게임 스레드로 복귀
    
    Game->>Game: Then() 콜백 실행
```

## 2. CID 기반 라우팅

- CID 12345 → Thread 5 (12345 % 32 = 5)
- CID 12346 → Thread 6 (12346 % 32 = 6)
- CID 12377 → Thread 5 (12377 % 32 = 5) ← 같은 스레드!

## 3. 에러 처리 흐름

```mermaid
graph TD
    A[쿼리 실행] --> B{성공?}
    B -->|Yes| C[Promise.SetValue]
    B -->|No| D{에러 타입}
    D -->|Deadlock| E[재시도 3회]
    D -->|Timeout| F[에러 로깅]
    D -->|기타| G[예외 전파]
    E --> A
    F --> H[Promise.SetException]
    G --> H
```
*/
```

#### 개발자 가이드
```markdown
# Database_Maria 개발자 가이드

## 빠른 시작

### 1. 간단한 쿼리 실행
```cpp
// 캐릭터 정보 조회
auto promise = NSDataBaseManager::GetInstance()->StartQuery<SpGetCharacterInfo>(
    connection,
    serializer,
    transactionId
);

promise.Then([](auto queryData) {
    auto recordSet = queryData->GetRecordSet();
    if (recordSet && !recordSet->IsEOF()) {
        int64_t cid;
        std::string name;
        recordSet->GetItem("CID", cid);
        recordSet->GetItem("Name", name);
    }
});
```

### 2. 디버깅 방법

#### 쿼리 추적
1. 로그에서 `[QUERY_START]` 검색
2. Query ID로 전체 흐름 추적
3. `[QUERY_END]`에서 실행 시간 확인

#### 느린 쿼리 찾기
```
grep "elapsed=[0-9]{4,}" db.log  # 1000ms 이상 쿼리
```

#### 에러 분석
```
grep "[QUERY_ERROR]" db.log | grep "cid=12345"  # 특정 캐릭터 에러
```

### 3. 성능 최적화 팁

- 같은 CID의 쿼리는 순차 실행됨 (병렬 불가)
- 다른 CID는 병렬 실행 가능
- PreparedStatement는 자동 캐싱됨
- 배치 쿼리 사용 시 네트워크 왕복 감소

### 4. 주의사항

- `IsEOF()`는 자동으로 다음 행으로 이동
- 트랜잭션은 같은 CID 내에서만 유효
- 데드락 시 자동 재시도 (3회)
```

### Day 15: 테스트 및 배포

#### 성능 테스트
```cpp
// PerformanceTest.cpp
class DatabasePerformanceTest {
public:
    void RunBenchmark() {
        const int QUERY_COUNT = 10000;
        const int THREAD_COUNT = 32;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        std::vector<std::future<void>> futures;
        for (int i = 0; i < THREAD_COUNT; ++i) {
            futures.push_back(std::async(std::launch::async, [i, QUERY_COUNT]() {
                for (int j = 0; j < QUERY_COUNT / THREAD_COUNT; ++j) {
                    // 테스트 쿼리 실행
                    TestQuery(i * 1000 + j);  // 각 스레드별 다른 CID
                }
            }));
        }
        
        for (auto& f : futures) {
            f.wait();
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>
                       (endTime - startTime);
        
        double qps = (double)QUERY_COUNT / duration.count() * 1000;
        
        std::cout << "Total Queries: " << QUERY_COUNT << "\n";
        std::cout << "Total Time: " << duration.count() << "ms\n";
        std::cout << "QPS: " << qps << "\n";
        
        // 성능 기준: 기존 대비 ±5% 이내
        ASSERT_TRUE(qps >= BASELINE_QPS * 0.95);
    }
};
```

---

## 5. 구체적 구현 예시

### 전체 통합 예시
```cpp
// 실제 사용 시나리오
void GameLogic::LoadCharacterData(int64_t cid) {
    // 1. 쿼리 시작 - 디버그 정보 자동 수집
    auto promise = NSDataBaseManager::GetInstance()->StartQuery<SpGetCharacterInfo>(
        Connection{DatabaseType::GameDB, cid % 10},  // 샤드 자동 선택
        Serializer{cid}
    );
    
    // 2. 결과 처리 - 원래 스레드에서 실행
    promise.Then([this, cid](auto queryData) {
        // QueryContext를 통해 실행 정보 확인 가능
        auto* ctx = queryData->GetContext();
        if (ctx->GetTotalElapsedMs() > 100) {
            LOGW << "Slow query detected: " << ctx->GetFlowSummary();
        }
        
        // 3. 데이터 처리
        auto recordSet = queryData->GetRecordSet();
        if (recordSet && !recordSet->IsEOF()) {
            Character character;
            recordSet->GetItem("Level", character.level);
            recordSet->GetItem("Name", character.name);
            recordSet->GetItem("Gold", character.gold);
            
            // 4. 게임 로직 계속
            OnCharacterLoaded(character);
        }
    }).Catch([cid](std::exception_ptr e) {
        // 5. 에러 처리 - 상세 컨텍스트 포함
        try {
            std::rethrow_exception(e);
        } catch (const DatabaseException& dbEx) {
            LOGE << "Failed to load character " << cid 
                 << ": " << dbEx.GetContext().ToString();
        }
    });
}
```

---

## 6. 테스트 및 검증

### 체크리스트

- [ ] 모든 기존 단위 테스트 통과
- [ ] 성능 벤치마크 ±5% 이내
- [ ] 메모리 사용량 증가 10% 이내
- [ ] 신규 개발자 온보딩 테스트
- [ ] 프로덕션 카나리 배포 (1개 서버)
- [ ] 24시간 모니터링
- [ ] 전체 배포

### 롤백 계획

```cpp
// Feature Toggle로 즉시 비활성화 가능
DatabaseConfig::SetDebugMode(false);
DatabaseConfig::SetUseNewFlowTracking(false);
```

---

## 📊 예상 결과

### Before
- 에러 발생 → "쿼리 실패" → 원인 파악 2시간
- 신규 개발자 → 코드 분석 2주 → 첫 기능 구현

### After  
- 에러 발생 → 상세 로그 → 원인 파악 10분
- 신규 개발자 → 가이드 + 예제 → 3일 만에 기능 구현

### ROI
- 투자: 3주 (300만원)
- 월간 절감: 디버깅 시간 40시간 → 디버깅 시간 절감으로 월 200만원 가치
- 회수 기간: 1.5개월

---

이것이 실제로 3주 안에 완료 가능한 현실적인 리팩토링 계획입니다.