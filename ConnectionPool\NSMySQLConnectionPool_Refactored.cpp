#include "../stdafx.h"
#include "NSMySQLConnectionPool_Refactored.h"
#include "../Connection/NSMySQLConnection.h"
#include <chrono>
#include <algorithm>

NSMySQLConnectionPool::NSMySQLConnectionPool()
{
}

NSMySQLConnectionPool::~NSMySQLConnectionPool()
{
    Finalize();
}

bool NSMySQLConnectionPool::Initialize(int databaseType, int shardId)
{
    bool expected = false;
    if (!m_initialized.compare_exchange_strong(expected, true))
        return true; // 이미 초기화됨

    m_databaseType = databaseType;
    m_shardId = shardId;

    // 연결 정보 로드
    if (!LoadConnectionInfo())
    {
        m_initialized = false;
        return false;
    }

    // 최소 연결 수만큼 미리 생성
    for (int i = 0; i < m_minConnections.load(); ++i)
    {
        auto conn = CreateConnection();
        if (conn)
        {
            std::lock_guard<std::mutex> lock(m_sharedPoolMutex);
            m_sharedPool.push(conn);
        }
        else
        {
            // 초기화 실패
            LOGE << "Failed to create initial connections for pool " 
                 << databaseType << ":" << shardId;
            Finalize();
            return false;
        }
    }

    // 정리 스레드 시작
    m_cleanupThread = std::thread([this]() {
        while (!m_shutting_down.load())
        {
            std::unique_lock<std::mutex> lock(m_cleanupMutex);
            m_cleanupCv.wait_for(lock, std::chrono::minutes(1), 
                [this]() { return m_shutting_down.load(); });
            
            if (!m_shutting_down.load())
            {
                CleanupIdleConnections();
            }
        }
    });

    LOGI << "ConnectionPool initialized: type=" << databaseType 
         << " shard=" << shardId 
         << " connections=" << m_totalConnections.load();
         
    return true;
}

void NSMySQLConnectionPool::Finalize()
{
    bool expected = true;
    if (!m_initialized.compare_exchange_strong(expected, false))
        return; // 이미 종료됨

    m_shutting_down = true;

    // 정리 스레드 종료
    if (m_cleanupThread.joinable())
    {
        m_cleanupCv.notify_all();
        m_cleanupThread.join();
    }

    // 모든 스레드별 연결 정리
    for (auto& tc : m_threadConnections)
    {
        std::lock_guard<std::mutex> lock(tc.mutex);
        tc.connection.reset();
        tc.inUse = false;
    }

    // 공용 풀 정리
    {
        std::lock_guard<std::mutex> lock(m_sharedPoolMutex);
        while (!m_sharedPool.empty())
        {
            m_sharedPool.pop();
        }
    }

    m_activeConnections = 0;
    m_totalConnections = 0;
    
    LOGI << "ConnectionPool finalized: type=" << m_databaseType 
         << " shard=" << m_shardId;
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection(int threadIndex)
{
    if (!m_initialized.load())
        return nullptr;

    // 스레드 인덱스 정규화 (CID 라우팅 보장)
    threadIndex = ((threadIndex % MAX_THREADS) + MAX_THREADS) % MAX_THREADS;

    // 1. 스레드별 전용 커넥션 확인
    {
        auto& tc = m_threadConnections[threadIndex];
        std::lock_guard<std::mutex> lock(tc.mutex);
        
        // 이미 사용 중이 아니고 유효한 연결이 있는 경우
        if (!tc.inUse.load() && tc.connection && ValidateConnection(tc.connection))
        {
            tc.inUse = true;
            tc.lastUsed = std::chrono::steady_clock::now();
            tc.queryCount++;
            m_totalQueries++;
            
            return tc.connection;
        }
        
        // 전용 연결이 없거나 유효하지 않은 경우 새로 생성
        if (!tc.inUse.load())
        {
            auto newConn = CreateConnection();
            if (newConn)
            {
                tc.connection = newConn;
                tc.inUse = true;
                tc.lastUsed = std::chrono::steady_clock::now();
                tc.queryCount++;
                m_totalQueries++;
                
                return tc.connection;
            }
        }
    }

    // 2. 전용 커넥션을 못 가져온 경우 공용 풀에서 가져오기
    std::unique_lock<std::mutex> lock(m_sharedPoolMutex);

    // 타임아웃 설정 (30초)
    auto timeout = std::chrono::steady_clock::now() + std::chrono::seconds(30);
    
    // 사용 가능한 연결이 있을 때까지 대기
    if (!m_sharedPoolCv.wait_until(lock, timeout, [this]() {
        return !m_sharedPool.empty() || 
               m_totalConnections.load() < m_maxConnections.load() ||
               m_shutting_down.load();
    }))
    {
        LOGE << "Timeout waiting for available connection";
        return nullptr;
    }

    if (m_shutting_down.load())
        return nullptr;

    // 풀에서 연결 가져오기
    if (!m_sharedPool.empty())
    {
        auto conn = m_sharedPool.front();
        m_sharedPool.pop();
        
        if (ValidateConnection(conn))
        {
            m_activeConnections++;
            m_totalQueries++;
            return conn;
        }
        else
        {
            m_totalConnections--;
            m_failedConnections++;
        }
    }

    // 3. 새 연결 생성
    if (m_totalConnections.load() < m_maxConnections.load())
    {
        lock.unlock(); // 연결 생성 중에는 락 해제
        
        auto conn = CreateConnection();
        if (conn)
        {
            m_activeConnections++;
            m_totalQueries++;
            return conn;
        }
        else
        {
            m_failedConnections++;
            LOGE << "Failed to create new connection. Total: " 
                 << m_totalConnections.load();
        }
    }

    return nullptr;
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection()
{
    int index = m_nextThreadIndex.fetch_add(1);
    return GetConnection(index);
}

void NSMySQLConnectionPool::ReturnConnection(std::shared_ptr<NSMySQLConnection> conn, int threadIndex)
{
    if (!conn || m_shutting_down.load())
        return;

    // 스레드 인덱스 정규화
    threadIndex = ((threadIndex % MAX_THREADS) + MAX_THREADS) % MAX_THREADS;

    // 연결 유효성 확인
    if (!ValidateConnection(conn))
    {
        m_totalConnections--;
        m_activeConnections--;
        return;
    }

    // 1. 스레드별 전용 슬롯에 반환 시도
    {
        auto& tc = m_threadConnections[threadIndex];
        std::lock_guard<std::mutex> lock(tc.mutex);
        
        if (tc.connection == conn)
        {
            tc.inUse = false;
            tc.lastUsed = std::chrono::steady_clock::now();
            return;
        }
    }

    // 2. 공용 풀에 반환
    {
        std::lock_guard<std::mutex> lock(m_sharedPoolMutex);
        
        // 풀이 최대 크기를 초과하지 않도록
        if (m_sharedPool.size() < static_cast<size_t>(m_maxConnections.load()))
        {
            m_sharedPool.push(conn);
            m_activeConnections--;
            m_sharedPoolCv.notify_one();
            return;
        }
    }

    // 3. 풀이 가득 찬 경우 연결 닫기
    m_totalConnections--;
    m_activeConnections--;
}

void NSMySQLConnectionPool::ReturnConnection(std::shared_ptr<NSMySQLConnection> conn)
{
    if (!conn)
        return;
        
    // 라운드 로빈으로 스레드 선택
    int index = m_nextThreadIndex.fetch_add(1);
    ReturnConnection(conn, index);
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::CreateConnection()
{
    auto conn = std::make_shared<NSMySQLConnection>();
    
    if (conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                     m_password.c_str(), m_database.c_str()))
    {
        m_totalConnections++;
        return conn;
    }
    
    LOGE << "Failed to create connection to " 
         << m_host << ":" << m_port << "/" << m_database;
    return nullptr;
}

bool NSMySQLConnectionPool::ValidateConnection(const std::shared_ptr<NSMySQLConnection>& conn)
{
    if (!conn)
        return false;
        
    // 연결 상태 확인 (간단한 쿼리 실행)
    return conn->CheckConnection();
}

void NSMySQLConnectionPool::CleanupIdleConnections()
{
    auto now = std::chrono::steady_clock::now();
    int cleaned = 0;
    
    // 스레드별 유휴 연결 정리
    for (auto& tc : m_threadConnections)
    {
        std::lock_guard<std::mutex> lock(tc.mutex);
        
        if (!tc.inUse.load() && tc.connection)
        {
            auto idleTime = now - tc.lastUsed;
            if (idleTime > IDLE_CONNECTION_TIMEOUT)
            {
                tc.connection.reset();
                cleaned++;
            }
        }
    }
    
    if (cleaned > 0)
    {
        m_totalConnections -= cleaned;
        LOGI << "Cleaned up " << cleaned << " idle connections. Total: " 
             << m_totalConnections.load();
    }
}

bool NSMySQLConnectionPool::IsHealthy() const
{
    if (!m_initialized.load() || m_shutting_down.load())
        return false;
        
    // 실패율 확인 (5% 이상이면 unhealthy)
    int64_t total = m_totalQueries.load();
    int64_t failed = m_failedConnections.load();
    
    if (total > 100 && failed > 0)
    {
        double failureRate = static_cast<double>(failed) / total;
        if (failureRate > 0.05)
            return false;
    }
    
    // 최소 연결 수 확인
    return m_totalConnections.load() >= m_minConnections.load();
}

bool NSMySQLConnectionPool::LoadConnectionInfo()
{
    // 실제 구현에서는 설정 파일이나 환경 변수에서 로드
    // 여기서는 예시로 하드코딩
    
    switch (m_databaseType)
    {
    case 0: // GameDB
        m_host = "localhost";
        m_port = 3306;
        m_user = "game_user";
        m_password = "game_password";
        m_database = "GameDB_" + std::to_string(m_shardId);
        break;
        
    case 10: // CommonDB
        m_host = "localhost";
        m_port = 3306;
        m_user = "common_user";
        m_password = "common_password";
        m_database = "CommonDB";
        break;
        
    case 11: // LogDB
        m_host = "localhost";
        m_port = 3306;
        m_user = "log_user";
        m_password = "log_password";
        m_database = "LogDB";
        break;
        
    default:
        LOGE << "Unknown database type: " << m_databaseType;
        return false;
    }
    
    return true;
}