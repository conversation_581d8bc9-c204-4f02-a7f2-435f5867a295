#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "Threading/ThreadedWorkManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include "DatabaseLogger.h" // 구조화된 로깅
#include <thread>
#include <sstream>

// 스레드 로컬 시퀀스 스토리지 정의
thread_local NSDataBaseManager::ThreadLocalSequence NSDataBaseManager::t_localSequence;

template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    const NS::Connection& connection,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // 연결 풀 가져오기
        auto* pool = GetConnectionPool(connection.DatabaseType, connection.ShardId);
        if (!pool)
        {
            m_pushAccessCount--;
            promise.SetException(std::make_exception_ptr(
                std::runtime_error("Invalid connection pool")));
            return;
        }

        // 샤드키 기반 스레드 선택
        uint64_t shardKey = 0;
        if constexpr (requires { SP::Input; })
        {
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            if constexpr (requires { input.Cid; })
                shardKey = input.Cid;
            else if constexpr (requires { input.Aid; })
                shardKey = input.Aid;
        }
        
        int threadIndex = GetExecutorByShardKey(shardKey);
        
        // CID 라우팅 로깅
        DB_LOG_CID_ROUTING(shardKey, threadIndex, 
                          "CID " + std::to_string(shardKey) + " % " + 
                          std::to_string(m_threadCount.value_or(0)) + " = " + 
                          std::to_string(threadIndex));
        
        m_queriesProcessing++;
        m_inputCount++;
        
        // 스레드별 워커에서 비동기 실행
        m_workerManager->PostWork([=]() mutable
        {
            // 쿼리 로깅 컨텍스트 생성 (자동으로 시작 로그)
            QueryLogContext logContext(SP::GetName(), shardKey, threadIndex);
            
            // RAII 가드로 atomic counter 보장
            struct CounterGuard {
                std::atomic<int>& queries;
                std::atomic<int>& access;
                ~CounterGuard() {
                    queries--;
                    access--;
                }
            } guard{m_queriesProcessing, m_pushAccessCount};

            auto queryData = std::make_shared<NSQueryData>();
            std::unique_ptr<NSMySQLConnection> conn;
            
            try
            {
                logContext.LogStep("Getting connection from pool");
                
                // 연결 획득
                conn = pool->GetConnection(threadIndex);
                bool connSuccess = (conn != nullptr);
                
                DB_LOG_POOL_GET(connection.DatabaseType, connection.ShardId, 
                               threadIndex, connSuccess);
                
                if (!conn)
                {
                    throw std::runtime_error("Failed to get connection");
                }

                logContext.LogStep("Executing stored procedure");
                
                // 저장 프로시저 실행
                SP sp;
                auto startTime = std::chrono::steady_clock::now();
                auto result = sp.Execute(conn.get(), serializer);
                auto execTime = std::chrono::duration_cast<std::chrono::milliseconds>
                               (std::chrono::steady_clock::now() - startTime).count();
                
                queryData->SetErrorCode(result);
                
                // 데드락 감지
                if (result == 1213) { // MySQL 데드락 에러 코드
                    DB_LOG_DEADLOCK(logContext.queryId, SP::GetName(), 0);
                }
                
                m_outputCount++;
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                {
                    logContext.LogStep("Executing after-query callback");
                    m_afterExecuteQuery(*queryData);
                }

                // 결과 반환
                promise.SetValue(queryData);
                
                // 정상 종료 로그
                logContext.LogEnd(true);

                // 연결 반환
                DB_LOG_POOL_RETURN(connection.DatabaseType, connection.ShardId, threadIndex);
                pool->ReturnConnection(std::move(conn), threadIndex);
            }
            catch (const std::exception& e)
            {
                // 연결 반환 보장
                if (conn)
                {
                    DB_LOG_POOL_RETURN(connection.DatabaseType, connection.ShardId, threadIndex);
                    pool->ReturnConnection(std::move(conn), threadIndex);
                }
                
                // 에러 로깅
                std::stringstream context;
                context << "sp=" << SP::GetName() 
                       << " thread=" << threadIndex 
                       << " cid=" << shardKey;
                
                logContext.LogError(e.what(), context.str());
                
                promise.SetException(std::current_exception());
            }
            catch (...)
            {
                // 연결 반환 보장
                if (conn)
                {
                    DB_LOG_POOL_RETURN(connection.DatabaseType, connection.ShardId, threadIndex);
                    pool->ReturnConnection(std::move(conn), threadIndex);
                }
                
                std::stringstream context;
                context << "sp=" << SP::GetName() 
                       << " thread=" << threadIndex;
                
                logContext.LogError("Unknown exception", context.str());
                
                promise.SetException(std::current_exception());
            }
            
            // 통계 로깅 (주기적으로)
            static std::atomic<int64_t> queryCounter{0};
            if (++queryCounter % 1000 == 0) // 1000개마다
            {
                auto active = m_queriesProcessing.load();
                auto total = m_outputCount.load();
                auto failed = 0; // TODO: 실패 카운터 추가
                auto avgTime = 0; // TODO: 평균 시간 계산
                
                DB_LOG_STATS(active, total, failed, avgTime);
            }
        }, threadIndex);
    });
}

// 트랜잭션 메서드들도 로깅 추가
DBPromise<bool> NSDataBaseManager::BeginTransaction(int64_t cid)
{
    int64_t transactionId = GenerateTransactionId();
    DB_LOG_TRANSACTION_BEGIN(transactionId, cid);
    
    // ... 기존 트랜잭션 시작 코드 ...
    
    return DBPromise<bool>::CreateResolved(true);
}

DBPromise<bool> NSDataBaseManager::CommitTransaction(int64_t transactionId)
{
    DB_LOG_TRANSACTION_END(transactionId, "commit");
    
    // ... 기존 커밋 코드 ...
    
    return DBPromise<bool>::CreateResolved(true);
}

DBPromise<bool> NSDataBaseManager::RollbackTransaction(int64_t transactionId)
{
    DB_LOG_TRANSACTION_END(transactionId, "rollback");
    
    // ... 기존 롤백 코드 ...
    
    return DBPromise<bool>::CreateResolved(true);
}