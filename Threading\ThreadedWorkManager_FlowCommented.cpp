#include "../stdafx.h"
#include "ThreadedWorkManager.h"
#include <windows.h>
#include <processthreadsapi.h>

ThreadedWorkManager::ThreadedWorkManager()
{
}

ThreadedWorkManager::~ThreadedWorkManager()
{
    Finalize();
}

bool ThreadedWorkManager::Initialize(int workerCount)
{
    // ===== FLOW: 워커 매니저 초기화 =====
    // - DB 작업을 처리할 전용 스레드 풀 생성
    // - CID 기반 라우팅을 위한 고정 스레드 구성
    
    if (m_initialized)
        return false;

    if (workerCount <= 0 || workerCount > 64)
        return false;

    m_workers.reserve(workerCount);

    // FLOW: 각 워커 스레드 생성
    for (int i = 0; i < workerCount; ++i)
    {
        auto worker = std::make_unique<WorkerThread>();
        worker->name = "DBWorker#" + std::to_string(i);
        worker->running = true;
        
        // FLOW: 워커 스레드 시작 - 각 스레드는 독립적인 큐를 가짐
        worker->thread = std::thread([this, i]() { WorkerProc(i); });
        
        m_workers.push_back(std::move(worker));
    }

    m_initialized = true;
    return true;
}

void ThreadedWorkManager::Finalize()
{
    // ===== FLOW: 워커 매니저 종료 =====
    // - 모든 워커 스레드에 종료 신호
    // - 진행 중인 작업 완료 대기
    
    if (!m_initialized)
        return;

    // FLOW: 모든 워커에 종료 신호 전송
    for (auto& worker : m_workers)
    {
        worker->running = false;
        worker->cv.notify_one(); // 대기 중인 스레드 깨우기
    }

    // FLOW: 모든 워커 스레드 종료 대기
    for (auto& worker : m_workers)
    {
        if (worker->thread.joinable())
        {
            worker->thread.join();
        }
    }

    m_workers.clear();
    m_initialized = false;
}

void ThreadedWorkManager::PostWork(WorkFunction work, int threadIndex)
{
    // ===== FLOW: 특정 스레드에 작업 전달 =====
    // - 필수 규칙: 같은 CID는 항상 같은 threadIndex
    // - NSDataBaseManager가 CID % 스레드수로 threadIndex 계산
    // - 해당 스레드의 큐에 작업 추가
    
    if (!m_initialized || !work)
        return;

    // FLOW: 스레드 인덱스 정규화 (음수나 범위 초과 처리)
    if (threadIndex < 0 || threadIndex >= static_cast<int>(m_workers.size()))
    {
        threadIndex = ((threadIndex % m_workers.size()) + m_workers.size()) % m_workers.size();
    }

    auto& worker = m_workers[threadIndex];
    
    // FLOW: 해당 스레드의 큐에 작업 추가
    {
        std::lock_guard<std::mutex> lock(worker->queueMutex);
        worker->queue.push(std::move(work));
        worker->queueSize++;
    }
    
    // FLOW: 해당 워커 스레드 깨우기 (대기 중이었다면)
    worker->cv.notify_one();
}

void ThreadedWorkManager::PostWork(WorkFunction work)
{
    // ===== FLOW: 랜덤 스레드에 작업 전달 =====
    // - CID가 없는 작업용 (예: 통계, 로깅)
    // - 라운드 로빈으로 부하 분산
    
    if (!m_initialized || !work)
        return;

    // FLOW: 라운드 로빈 방식으로 다음 워커 선택
    int index = m_nextWorkerIndex.fetch_add(1) % m_workers.size();
    PostWork(std::move(work), index);
}

void ThreadedWorkManager::WorkerProc(int index)
{
    // ===== FLOW: DB 워커 스레드 메인 루프 =====
    // - 현재 스레드: DB Worker Thread[index]
    // - 게임 스레드와 분리되어 DB 작업 처리
    // - 자신의 큐에서만 작업을 가져옴
    
    auto& worker = *m_workers[index];
    
    // FLOW: 디버거에서 스레드 이름 표시
    SetThreadName(worker.name);

    // FLOW: 워커 스레드 메인 루프
    while (worker.running)
    {
        std::unique_lock<std::mutex> lock(worker.queueMutex);
        
        // FLOW: 작업이 있거나 종료 신호까지 대기
        // - 큐가 비어있으면 cv.wait()에서 블로킹
        // - PostWork()가 notify_one() 호출 시 깨어남
        worker.cv.wait(lock, [&worker]() {
            return !worker.queue.empty() || !worker.running;
        });

        // FLOW: 큐에 있는 모든 작업 처리
        while (!worker.queue.empty())
        {
            // FLOW: 큐에서 작업 하나 꺼내기
            auto work = std::move(worker.queue.front());
            worker.queue.pop();
            worker.queueSize--;

            // FLOW: 작업 실행 중에는 락 해제 (다른 작업 추가 가능)
            lock.unlock();
            
            try
            {
                // ===== FLOW: DB 작업 실행 =====
                // - 여기서 실제 DB 쿼리 실행
                // - NSDataBaseManager::StartQueryImpl의 람다 함수
                // - 프로시저 실행, 결과 처리, Promise 설정
                work();
                worker.processedCount++;
            }
            catch (...)
            {
                // FLOW: 예외 발생 시에도 계속 진행
                // - 하나의 작업 실패가 전체를 멈추지 않음
                LOGE << "Exception in worker thread " << index;
            }
            
            // FLOW: 다음 작업 처리를 위해 락 재획득
            lock.lock();
        }
    }
    
    // FLOW: 스레드 종료
}

void ThreadedWorkManager::SetThreadName(const std::string& name)
{
    // ===== FLOW: Windows 디버거용 스레드 이름 설정 =====
    // - Visual Studio 디버거에서 스레드 식별 용이
    
#ifdef _WIN32
    const DWORD MS_VC_EXCEPTION = 0x406D1388;

    #pragma pack(push,8)
    typedef struct tagTHREADNAME_INFO
    {
        DWORD dwType;
        LPCSTR szName;
        DWORD dwThreadID;
        DWORD dwFlags;
    } THREADNAME_INFO;
    #pragma pack(pop)

    THREADNAME_INFO info;
    info.dwType = 0x1000;
    info.szName = name.c_str();
    info.dwThreadID = GetCurrentThreadId();
    info.dwFlags = 0;

    __try
    {
        // FLOW: 디버거에 스레드 이름 전달
        RaiseException(MS_VC_EXCEPTION, 0, sizeof(info)/sizeof(ULONG_PTR), (ULONG_PTR*)&info);
    }
    __except(EXCEPTION_EXECUTE_HANDLER)
    {
        // FLOW: 디버거가 없으면 예외 무시
    }
#endif
}

int64_t ThreadedWorkManager::GetQueueSize(int threadIndex) const
{
    // FLOW: 특정 스레드의 대기 중인 작업 수 조회
    if (threadIndex < 0 || threadIndex >= static_cast<int>(m_workers.size()))
        return 0;

    return m_workers[threadIndex]->queueSize.load();
}

int64_t ThreadedWorkManager::GetTotalQueueSize() const
{
    // FLOW: 모든 스레드의 총 대기 작업 수 합계
    int64_t total = 0;
    for (const auto& worker : m_workers)
    {
        total += worker->queueSize.load();
    }
    return total;
}