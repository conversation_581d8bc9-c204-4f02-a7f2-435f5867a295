#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "Threading/ThreadedWorkManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include <thread>
#include <sstream>

// 스레드 로컬 시퀀스 스토리지 정의
thread_local NSDataBaseManager::ThreadLocalSequence NSDataBaseManager::t_localSequence;

NSDataBaseManager::NSDataBaseManager()
{
}

NSDataBaseManager::~NSDataBaseManager()
{
    Finalize();
}

bool NSDataBaseManager::Initialize()
{
    // ===== FLOW STEP: 초기화 시작 =====
    // - 싱글톤 인스턴스 초기화
    // - 워커 매니저와 연결 풀 준비
    
    if (m_initialized.exchange(true))
        return true;

    try
    {
        // FLOW: 스레드별 작업 매니저 생성 (비동기 실행용)
        m_workerManager = std::make_unique<ThreadedWorkManager>();
        
        // FLOW: 12개 연결 풀 생성 (GameDB 0-9, CommonDB, LogDB)
        // - GameDB: 0-9번 인덱스 (샤드별)
        // - CommonDB: 10번 인덱스
        // - LogDB: 11번 인덱스
        for (int i = 0; i < 12; ++i)
        {
            m_connectionPools[i] = std::make_unique<NSMySQLConnectionPool>();
        }

        return true;
    }
    catch (const std::exception& e)
    {
        m_initialized = false;
        return false;
    }
}

template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    const NS::Connection& connection,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // ===== FLOW STEP 1: 게임 스레드에서 DB 요청 시작 =====
    // - 호출자: 게임 로직 (예: 캐릭터 정보 조회, 아이템 업데이트)
    // - 현재 스레드: Game Logic Thread
    // - 목적: 비동기 DB 작업 시작
    
    // FLOW: 서버 종료 중이면 요청 거부
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    // FLOW: 진행 중인 요청 카운터 증가
    m_pushAccessCount++;
    
    // ===== FLOW STEP 2: Promise 생성 및 비동기 작업 설정 =====
    // - Promise는 미래의 결과를 나타냄
    // - 실제 DB 작업은 별도 스레드에서 실행됨
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // FLOW: 연결 풀 선택 (DatabaseType과 ShardId 기반)
        auto* pool = GetConnectionPool(connection.DatabaseType, connection.ShardId);
        if (!pool)
        {
            m_pushAccessCount--;
            promise.SetException(std::make_exception_ptr(
                std::runtime_error("Invalid connection pool")));
            return;
        }

        // ===== FLOW STEP 3: CID 기반 스레드 라우팅 =====
        // - 필수 규칙: 같은 CID는 항상 같은 스레드에서 처리
        // - 이유: 순서 보장, 트랜잭션 일관성
        // - 샤드키 추출: SP의 Input 구조체에서 Cid 또는 Aid 사용
        
        uint64_t shardKey = 0;
        if constexpr (requires { SP::Input; })
        {
            // FLOW: 프로시저 입력 파라미터에서 CID/AID 추출
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            
            // FLOW: CID 우선, 없으면 AID 사용
            if constexpr (requires { input.Cid; })
                shardKey = input.Cid;  // 캐릭터 ID
            else if constexpr (requires { input.Aid; })
                shardKey = input.Aid;  // 계정 ID
        }
        
        // FLOW: CID % 스레드수로 담당 스레드 결정
        int threadIndex = GetExecutorByShardKey(shardKey);
        // 예: CID 12345, 스레드 32개 → threadIndex = 12345 % 32 = 25
        
        // FLOW: 통계 카운터 증가
        m_queriesProcessing++;
        m_inputCount++;
        
        // ===== FLOW STEP 4: DB Worker Thread에 작업 전달 =====
        // - PostWork()는 지정된 스레드의 큐에 작업 추가
        // - threadIndex 스레드가 이 작업을 처리함
        
        m_workerManager->PostWork([=]() mutable
        {
            // ===== FLOW STEP 5: DB Worker Thread에서 실행 시작 =====
            // - 현재 스레드: DB Worker Thread[threadIndex]
            // - 게임 스레드와 분리되어 DB 작업 수행
            
            // FLOW: RAII 가드로 카운터 자동 감소 보장
            struct CounterGuard {
                std::atomic<int>& queries;
                std::atomic<int>& access;
                ~CounterGuard() {
                    queries--;
                    access--;
                }
            } guard{m_queriesProcessing, m_pushAccessCount};

            auto queryData = std::make_shared<NSQueryData>();
            std::unique_ptr<NSMySQLConnection> conn;
            
            try
            {
                // ===== FLOW STEP 6: DB 연결 획득 =====
                // - 필수 규칙: threadIndex별 전용 연결 사용
                // - 같은 CID는 항상 같은 연결 사용 → 순서 보장
                
                conn = pool->GetConnection(threadIndex);
                if (!conn)
                {
                    throw std::runtime_error("Failed to get connection");
                }

                // ===== FLOW STEP 7: 저장 프로시저 실행 =====
                // - SP 클래스가 실제 SQL 프로시저 호출
                // - serializer에서 파라미터 읽어 바인딩
                
                SP sp;  // 예: SpGetCharacterInfo, SpUpdateItem
                auto result = sp.Execute(conn.get(), serializer);
                queryData->SetErrorCode(result);
                
                // FLOW: 출력 카운터 증가
                m_outputCount++;
                
                // FLOW: 사용자 정의 콜백 실행 (있다면)
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);

                // ===== FLOW STEP 8: Promise에 결과 설정 =====
                // - 이 시점에서 대기 중인 게임 스레드에 알림
                // - Then() 콜백이 게임 스레드에서 실행됨
                
                promise.SetValue(queryData);

                // FLOW: 연결 반환 (같은 threadIndex로)
                pool->ReturnConnection(std::move(conn), threadIndex);
            }
            catch (const std::exception& e)
            {
                // FLOW: 에러 처리 - 연결 반환 보장
                if (conn)
                {
                    pool->ReturnConnection(std::move(conn), threadIndex);
                }
                
                // FLOW: 상세 에러 로깅
                LOGE << "Query execution failed: " << e.what() 
                     << " [SP: " << SP::GetName() 
                     << ", ThreadIndex: " << threadIndex 
                     << ", CID: " << shardKey << "]";
                
                // FLOW: Promise에 예외 전달
                promise.SetException(std::current_exception());
            }
            catch (...)
            {
                // FLOW: 알 수 없는 예외 처리
                if (conn)
                {
                    pool->ReturnConnection(std::move(conn), threadIndex);
                }
                
                LOGE << "Unknown exception in query execution"
                     << " [SP: " << SP::GetName() 
                     << ", ThreadIndex: " << threadIndex << "]";
                
                promise.SetException(std::current_exception());
            }
        }, threadIndex);  // 중요: threadIndex 전달로 특정 스레드 지정
    });
    
    // ===== FLOW STEP 9: Promise 반환 =====
    // - 호출자는 Then()으로 결과 처리 콜백 등록
    // - 실제 DB 작업은 백그라운드에서 진행
    // - 게임 로직은 블로킹 없이 계속 실행
}

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    // ===== FLOW: DB 매니저 시작 =====
    // - 워커 스레드 풀 생성
    // - 연결 풀 초기화
    
    if (!m_initialized)
        return false;
        
    // FLOW: 스레드 수 결정 (0이면 자동 계산)
    if (workThreadCnt == 0)
        workThreadCnt = std::thread::hardware_concurrency() * 2 + 16;
    
    m_threadCount = workThreadCnt;
    
    // FLOW: 워커 매니저 시작 (스레드 풀 생성)
    if (!m_workerManager->Initialize(workThreadCnt))
        return false;
        
    // FLOW: 각 DB별 연결 풀 초기화
    
    // GameDB 0-9 (10개 샤드)
    for (int i = 0; i < 10; ++i)
    {
        if (!m_connectionPools[i]->Initialize(0, i)) // DatabaseType=0, ShardId=i
            return false;
    }

    // CommonDB (인덱스 10) - 샤딩 없음
    if (!m_connectionPools[10]->Initialize(1, 0)) // DatabaseType=1, ShardId=0
        return false;

    // LogDB (인덱스 11) - 샤딩 없음
    if (!m_connectionPools[11]->Initialize(2, 0)) // DatabaseType=2, ShardId=0
        return false;

    return true;
}

void NSDataBaseManager::Stop()
{
    // ===== FLOW: DB 매니저 종료 =====
    // - 새 요청 차단
    // - 진행 중인 작업 완료 대기
    // - 워커 스레드 종료
    
    // FLOW: 새로운 요청 차단
    ProhibitPushAccess();
    
    // FLOW: 모든 워커 스레드 종료 및 대기
    StopAllWorkerThreadAndWait();
}

void NSDataBaseManager::ProhibitPushAccess()
{
    // FLOW: 새로운 DB 요청 차단 플래그 설정
    m_pushAccessProhibit = true;
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
    // FLOW: 진행 중인 모든 요청 완료 대기
    while (m_pushAccessCount.load() > 0)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // FLOW: 워커 매니저 종료 (모든 스레드 정지)
    if (m_workerManager)
    {
        m_workerManager->Finalize();
    }
}

int NSDataBaseManager::GetExecutorByShardKey(uint64_t shardKey) const
{
    // ===== FLOW: CID → 스레드 인덱스 매핑 =====
    // - 필수 규칙: 같은 CID는 항상 같은 스레드
    // - 알고리즘: CID % 스레드수
    // - 결과: 0 ~ (스레드수-1) 범위의 인덱스
    
    if (!m_threadCount.has_value() || m_threadCount.value() == 0)
        return 0;
        
    return static_cast<int>(shardKey % m_threadCount.value());
}

NSMySQLConnectionPool* NSDataBaseManager::GetConnectionPool(int32 dbType, int32 shardId)
{
    // ===== FLOW: DB 타입과 샤드ID로 연결 풀 선택 =====
    // - GameDB (0): 샤드 0-9 → 인덱스 0-9
    // - CommonDB (1): 샤드 무시 → 인덱스 10
    // - LogDB (2): 샤드 무시 → 인덱스 11
    
    int poolIndex = -1;

    switch (dbType)
    {
    case 0: // GameDB
        if (shardId >= 0 && shardId < 10)
            poolIndex = shardId;
        break;
    case 1: // CommonDB
        poolIndex = 10;
        break;
    case 2: // LogDB
        poolIndex = 11;
        break;
    }

    if (poolIndex >= 0 && poolIndex < 12)
        return m_connectionPools[poolIndex].get();

    return nullptr;
}

// ===== Storage 시퀀스 관리 =====
// - CID별 시퀀스로 업데이트 순서 보장
// - 스레드 로컬 스토리지 사용 (락 불필요)

int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    // FLOW: CID별 시퀀스 증가 (스레드 로컬)
    // - 같은 CID는 같은 스레드에서만 처리되므로 안전
    return ++t_localSequence.sequenceByCid[cid];
}

void NSDataBaseManager::OnSessionClosed(int64_t cid)
{
    // FLOW: 세션 종료 시 시퀀스 정리
    // - 메모리 누수 방지
    t_localSequence.sequenceByCid.erase(cid);
}